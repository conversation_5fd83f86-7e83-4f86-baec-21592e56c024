'use server'

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export async function createTicket(formData: FormData) {
  const cookieStore = await cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  const title = formData.get('title') as string
  const description = formData.get('description') as string
  const priority = formData.get('priority') as string

  if (!title || !description) {
    return { error: 'يرجى ملء جميع الحقول المطلوبة' }
  }

  try {
    const { data, error } = await supabase
      .from('tickets')
      .insert({
        title: title.trim(),
        description: description.trim(),
        priority: priority as 'low' | 'medium' | 'high',
        created_by: user.id,
        status: 'open'
      })
      .select()

    if (error) {
      console.error('Database error:', error)
      return { error: `حدث خطأ في قاعدة البيانات: ${error.message}` }
    }

    if (!data || data.length === 0) {
      return { error: 'لم يتم إنشاء الطلب بنجاح' }
    }

    console.log('Ticket created successfully:', data[0])
    
  } catch (error: any) {
    console.error('Error creating ticket:', error)
    return { error: `حدث خطأ غير متوقع: ${error.message}` }
  }

  redirect('/dashboard/tickets')
}

export async function getTickets(statusFilter: 'all' | 'open' | 'in_progress' | 'closed' = 'all') {
  try {
    const cookieStore = await cookies()

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      console.log('No authenticated user found')
      return { data: [] } // Return empty array instead of error
    }

    console.log('Fetching tickets for user:', user.id, 'with filter:', statusFilter)

    // Simple query to avoid join issues
    let query = supabase
      .from('tickets')
      .select('*')
      .order('created_at', { ascending: false })

    if (statusFilter !== 'all') {
      query = query.eq('status', statusFilter)
    }

    const { data, error } = await query

    if (error) {
      console.error('Database error:', error)
      return { data: [] } // Return empty array instead of error
    }

    // Get all unique user IDs from tickets
    const userIds = new Set()
    const ticketIds = []

    if (data && data.length > 0) {
      data.forEach(ticket => {
        if (ticket.created_by) userIds.add(ticket.created_by)
        if (ticket.assigned_to) userIds.add(ticket.assigned_to)
        ticketIds.push(ticket.id)
      })
    }

    // Fetch all user information in one query
    const { data: usersData } = await supabase
      .from('profiles')
      .select('id, full_name, email, role')
      .in('id', Array.from(userIds))

    // Create user lookup map
    const userMap = {}
    if (usersData) {
      usersData.forEach(user => {
        userMap[user.id] = user
      })
    }

    // Get reply counts for all tickets in one query
    const { data: replyCounts } = await supabase
      .from('ticket_replies')
      .select('ticket_id')
      .in('ticket_id', ticketIds)

    // Create reply count map
    const replyCountMap = {}
    if (replyCounts) {
      replyCounts.forEach(reply => {
        replyCountMap[reply.ticket_id] = (replyCountMap[reply.ticket_id] || 0) + 1
      })
    }

    // Enhance tickets with user information and reply counts
    const enhancedTickets = (data || []).map(ticket => ({
      ...ticket,
      creator: userMap[ticket.created_by] || null,
      assignee: userMap[ticket.assigned_to] || null,
      reply_count: replyCountMap[ticket.id] || 0
    }))

    console.log('Successfully fetched tickets:', enhancedTickets.length)
    return { data: enhancedTickets }
  } catch (error: any) {
    console.error('Error fetching tickets:', error)
    return { data: [] } // Return empty array instead of error
  }
}

export async function getTicketDetails(ticketId: string) {
  try {
    const cookieStore = await cookies()

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    )

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      console.log('No authenticated user found for ticket details')
      return { error: 'غير مصرح لك بالوصول' }
    }

    console.log('Fetching ticket details for:', ticketId, 'user:', user.id)

    // Get current user profile
    const { data: currentUserProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    // Fetch ticket data first
    const { data: ticketData, error: ticketError } = await supabase
      .from('tickets')
      .select('*')
      .eq('id', ticketId)
      .single()

    if (ticketError) {
      console.error('Ticket fetch error:', ticketError)
      return { error: `حدث خطأ في تحميل تفاصيل الطلب: ${ticketError.message}` }
    }

    // Fetch creator information
    let creatorData = null
    if (ticketData.created_by) {
      const { data: creator } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .eq('id', ticketData.created_by)
        .single()
      creatorData = creator
    }

    // Fetch assignee information
    let assigneeData = null
    if (ticketData.assigned_to) {
      const { data: assignee } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .eq('id', ticketData.assigned_to)
        .single()
      assigneeData = assignee
    }

    // Fetch replies data
    const { data: repliesData, error: repliesError } = await supabase
      .from('ticket_replies')
      .select('*')
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true })

    if (repliesError) {
      console.error('Replies fetch error:', repliesError)
      return { error: `حدث خطأ في تحميل الردود: ${repliesError.message}` }
    }

    // Fetch user information for each reply
    const repliesWithUsers = []
    if (repliesData && repliesData.length > 0) {
      for (const reply of repliesData) {
        const { data: userData } = await supabase
          .from('profiles')
          .select('id, full_name, email, role')
          .eq('id', reply.user_id)
          .single()

        repliesWithUsers.push({
          ...reply,
          user: userData
        })
      }
    }

    // Enhanced reply permission check based on role hierarchy
    let canEmployeeReply = true
    if (currentUserProfile?.role === 'sales_employee' && ticketData.created_by === user.id) {
      // Sales employee can reply only after a manager has replied and ticket is still open
      const hasManagerReply = repliesWithUsers.some(reply =>
        reply.user?.role === 'team_manager' ||
        reply.user?.role === 'area_manager' ||
        reply.user?.role === 'system_admin'
      )
      canEmployeeReply = ticketData.status !== 'closed' && hasManagerReply
    }

    // Create enhanced ticket object with user information
    const enhancedTicket = {
      ...ticketData,
      creator: creatorData,
      assignee: assigneeData
    }

    console.log('Successfully fetched ticket details')
    return {
      ticket: enhancedTicket,
      replies: repliesWithUsers,
      canEmployeeReply,
      currentUserRole: currentUserProfile?.role
    }
  } catch (error: any) {
    console.error('Error fetching ticket details:', error)
    return { error: `حدث خطأ غير متوقع: ${error.message}` }
  }
}

export async function addTicketReply(ticketId: string, message: string) {
  const cookieStore = await cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'غير مصرح لك بالوصول' }
  }

  if (!message.trim()) {
    return { error: 'يرجى كتابة رسالة' }
  }

  try {
    const { error } = await supabase
      .from('ticket_replies')
      .insert({
        ticket_id: ticketId,
        user_id: user.id,
        message: message.trim()
      })

    if (error) {
      console.error('Reply insert error:', error)
      return { error: `حدث خطأ في إرسال الرد: ${error.message}` }
    }

    return { success: true }
  } catch (error: any) {
    console.error('Error adding reply:', error)
    return { error: `حدث خطأ غير متوقع: ${error.message}` }
  }
}

export async function updateTicketStatus(ticketId: string, status: 'open' | 'in_progress' | 'closed') {
  const cookieStore = await cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'غير مصرح لك بالوصول' }
  }

  try {
    const { error } = await supabase
      .from('tickets')
      .update({
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', ticketId)

    if (error) {
      console.error('Status update error:', error)
      return { error: `حدث خطأ في تحديث حالة الطلب: ${error.message}` }
    }

    return { success: true }
  } catch (error: any) {
    console.error('Error updating ticket status:', error)
    return { error: `حدث خطأ غير متوقع: ${error.message}` }
  }
}
