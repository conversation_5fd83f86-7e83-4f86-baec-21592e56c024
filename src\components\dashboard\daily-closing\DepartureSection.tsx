'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { LogOut, CheckCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase'

type DailyClosing = Database['public']['Tables']['daily_closings']['Row']
type AttendanceRecord = Database['public']['Tables']['attendance_records']['Row']

interface DepartureSectionProps {
  dailyClosing: DailyClosing
  onUpdate: () => void
  disabled?: boolean
}

export function DepartureSection({ dailyClosing, onUpdate, disabled }: DepartureSectionProps) {
  const [attendanceRecord, setAttendanceRecord] = useState<AttendanceRecord | null>(null)
  const [checkOutTime, setCheckOutTime] = useState('')
  const [notes, setNotes] = useState(dailyClosing.notes || '')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const supabase = createClient()

  useEffect(() => {
    fetchAttendanceRecord()
  }, [dailyClosing.id, dailyClosing.attendance_submitted]) // Re-fetch when attendance is submitted

  const fetchAttendanceRecord = async () => {
    try {
      console.log('Fetching attendance record for daily_closing_id:', dailyClosing.id)

      const { data, error } = await supabase
        .from('attendance_records')
        .select('*')
        .eq('daily_closing_id', dailyClosing.id)
        .single()

      console.log('Attendance record fetch result:', { data, error })

      if (data) {
        setAttendanceRecord(data)
        if (data.check_out_time) {
          setCheckOutTime(data.check_out_time)
        }
      } else if (error && error.code !== 'PGRST116') {
        console.error('Error fetching attendance record:', error)
        setError('فشل في تحميل بيانات الحضور: ' + error.message)
      } else if (error && error.code === 'PGRST116') {
        console.log('No attendance record found yet')
        setError('لم يتم العثور على سجل الحضور. يرجى التأكد من تسجيل الحضور أولاً.')
      }
    } catch (error: any) {
      console.error('Error in fetchAttendanceRecord:', error)
      setError('حدث خطأ في تحميل بيانات الحضور: ' + error.message)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!checkOutTime) {
      setError('يرجى إدخال وقت الانصراف')
      setLoading(false)
      return
    }

    if (!attendanceRecord) {
      setError('لم يتم العثور على سجل الحضور')
      setLoading(false)
      return
    }

    try {
      // Update attendance record with check-out time
      const { error: attendanceError } = await supabase
        .from('attendance_records')
        .update({
          check_out_time: checkOutTime,
          updated_at: new Date().toISOString()
        })
        .eq('id', attendanceRecord.id)

      if (attendanceError) throw attendanceError

      // Update daily closing to mark departure as submitted
      const { error: updateError } = await supabase
        .from('daily_closings')
        .update({
          departure_submitted: true,
          notes: notes.trim() || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', dailyClosing.id)

      if (updateError) throw updateError

      onUpdate()
    } catch (error: any) {
      console.error('Error submitting departure:', error)
      setError('حدث خطأ في حفظ بيانات الانصراف')
    } finally {
      setLoading(false)
    }
  }

  const getCurrentTime = () => {
    const now = new Date()
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  const fillCurrentTime = () => {
    setCheckOutTime(getCurrentTime())
  }

  const calculateWorkingHours = () => {
    if (!attendanceRecord?.check_in_time || !checkOutTime) return null

    const checkIn = new Date(`2000-01-01T${attendanceRecord.check_in_time}`)
    const checkOut = new Date(`2000-01-01T${checkOutTime}`)
    
    if (checkOut < checkIn) {
      // Handle next day checkout
      checkOut.setDate(checkOut.getDate() + 1)
    }

    const diffMs = checkOut.getTime() - checkIn.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    
    const hours = Math.floor(diffHours)
    const minutes = Math.round((diffHours - hours) * 60)
    
    return `${hours} ساعة و ${minutes} دقيقة`
  }

  return (
    <Card className={disabled ? 'opacity-50' : ''}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LogOut className="h-5 w-5" />
          تسجيل الانصراف
          {dailyClosing.departure_submitted && (
            <CheckCircle className="h-5 w-5 text-green-600" />
          )}
        </CardTitle>
        <CardDescription>
          سجل وقت انصرافك وأي ملاحظات إضافية
          {disabled && (
            <Badge variant="secondary" className="mr-2">
              يجب تسجيل المبيعات أولاً
            </Badge>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4" dir="rtl">
          {/* Check-in time display */}
          {attendanceRecord ? (
            <div className="bg-muted p-3 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">وقت الحضور:</span>
                <span className="font-medium">{attendanceRecord.check_in_time}</span>
              </div>
            </div>
          ) : (
            <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
              <div className="flex justify-between items-center">
                <span className="text-sm text-amber-700">لم يتم العثور على بيانات الحضور</span>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={fetchAttendanceRecord}
                  className="text-amber-700 border-amber-300 hover:bg-amber-100"
                >
                  تحديث
                </Button>
              </div>
            </div>
          )}

          {/* Check-out time input */}
          <div className="space-y-2">
            <Label htmlFor="checkOutTime">وقت الانصراف</Label>
            <div className="flex gap-2">
              <Input
                id="checkOutTime"
                type="time"
                value={checkOutTime}
                onChange={(e) => setCheckOutTime(e.target.value)}
                disabled={disabled || dailyClosing.departure_submitted}
                className="flex-1"
                required
              />
              {!disabled && !dailyClosing.departure_submitted && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={fillCurrentTime}
                  className="whitespace-nowrap"
                >
                  الوقت الحالي
                </Button>
              )}
            </div>
          </div>

          {/* Working hours calculation */}
          {checkOutTime && attendanceRecord && (
            <div className="bg-blue-50 p-3 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-700">إجمالي ساعات العمل:</span>
                <span className="font-medium text-blue-700">{calculateWorkingHours()}</span>
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات إضافية (اختياري)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={disabled || dailyClosing.departure_submitted}
              placeholder="أضف أي ملاحظات أو تعليقات حول اليوم..."
              rows={3}
            />
          </div>

          {/* Summary */}
          <div className="bg-muted p-4 rounded-lg space-y-2">
            <h4 className="font-medium">ملخص اليوم</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span>إجمالي المبيعات:</span>
                <span className="font-medium">{dailyClosing.total_sales_amount.toFixed(2)} ر.س</span>
              </div>
              <div className="flex justify-between">
                <span>المبلغ المسلم:</span>
                <span className="font-medium">{dailyClosing.cash_delivered.toFixed(2)} ر.س</span>
              </div>
              {(dailyClosing.advances_amount || 0) > 0 && (
                <div className="flex justify-between text-purple-600">
                  <span>السلف:</span>
                  <span className="font-medium">-{(dailyClosing.advances_amount || 0).toFixed(2)} ر.س</span>
                </div>
              )}
              {(dailyClosing.price_breaks_amount || 0) > 0 && (
                <div className="flex justify-between text-orange-600">
                  <span>كسر السعر:</span>
                  <span className="font-medium">-{(dailyClosing.price_breaks_amount || 0).toFixed(2)} ر.س</span>
                </div>
              )}
              {dailyClosing.deficit_amount > 0 ? (
                <div className="flex justify-between text-amber-600">
                  <span>العجز النهائي:</span>
                  <span className="font-medium">{dailyClosing.deficit_amount.toFixed(2)} ر.س</span>
                </div>
              ) : (
                <div className="flex justify-between text-green-600">
                  <span>لا يوجد عجز:</span>
                  <span className="font-medium">0.00 ر.س</span>
                </div>
              )}
            </div>
          </div>

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          {dailyClosing.departure_submitted ? (
            <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle className="h-4 w-4" />
              <span>تم تسجيل الانصراف بنجاح - تم إكمال التقفيل اليومي</span>
            </div>
          ) : (
            <Button 
              type="submit" 
              disabled={disabled || loading}
              className="w-full"
            >
              {loading ? 'جاري الحفظ...' : 'تسجيل الانصراف وإنهاء اليوم'}
            </Button>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
