'use client'

import { useEffect, useRef, useCallback } from 'react'
import { createClient } from '@/lib/supabase'

export function useSupabaseConnection() {
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  const isReconnectingRef = useRef(false)

  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible' && !isReconnectingRef.current) {
      // Browser regained focus, refresh the connection
      isReconnectingRef.current = true
      
      // Clear any existing timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }

      // Small delay to ensure the browser is fully active
      reconnectTimeoutRef.current = setTimeout(() => {
        try {
          // Force a fresh connection by getting session
          const supabase = createClient()
          supabase.auth.getSession().then(() => {
            console.log('Supabase connection refreshed after focus')
          }).catch((error) => {
            console.warn('Failed to refresh Supabase connection:', error)
          }).finally(() => {
            isReconnectingRef.current = false
          })
        } catch (error) {
          console.warn('Error refreshing Supabase connection:', error)
          isReconnectingRef.current = false
        }
      }, 500)
    }
  }, [])

  const handleOnline = useCallback(() => {
    // Network came back online
    if (!isReconnectingRef.current) {
      handleVisibilityChange()
    }
  }, [handleVisibilityChange])

  useEffect(() => {
    // Listen for visibility changes (tab focus/blur)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // Listen for network status changes
    window.addEventListener('online', handleOnline)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('online', handleOnline)
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [handleVisibilityChange, handleOnline])

  // Return a function to manually trigger reconnection
  const forceReconnect = useCallback(() => {
    if (!isReconnectingRef.current) {
      handleVisibilityChange()
    }
  }, [handleVisibilityChange])

  return { forceReconnect }
}