import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'
import { UserRole, ROLE_HIERARCHY } from '@/lib/supabase'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { email, password, full_name, phone, role } = await request.json()

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // Create user with admin API
    const { data: user, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: full_name || '',
        phone: phone || ''
      }
    })

    if (createError) {
      return NextResponse.json(
        { error: createError.message },
        { status: 400 }
      )
    }

    // Validate role
    const userRole = (role || 'sales_employee') as UserRole
    if (!ROLE_HIERARCHY[userRole]) {
      return NextResponse.json(
        { error: 'دور غير صحيح' },
        { status: 400 }
      )
    }

    // Update profile with role, role_level and phone
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        role: userRole,
        role_level: ROLE_HIERARCHY[userRole].level,
        full_name: full_name || '',
        phone: phone || null
      })
      .eq('id', user.user.id)

    if (updateError) {
      return NextResponse.json(
        { error: updateError.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      user: {
        id: user.user.id,
        email: user.user.email
      }
    })

  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
