'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  src?: string
  alt?: string
  name?: string | null
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallbackClassName?: string
}

const sizeClasses = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8', 
  lg: 'h-12 w-12',
  xl: 'h-20 w-20'
}

const iconSizeClasses = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4',
  lg: 'h-6 w-6', 
  xl: 'h-8 w-8'
}

export function UserAvatar({ 
  src, 
  alt, 
  name, 
  className, 
  size = 'md',
  fallbackClassName 
}: UserAvatarProps) {
  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage src={src} alt={alt || name || 'مستخدم'} />
      <AvatarFallback className={cn('bg-muted', fallbackClassName)}>
        <User className={cn(iconSizeClasses[size], 'text-muted-foreground')} />
      </AvatarFallback>
    </Avatar>
  )
}
