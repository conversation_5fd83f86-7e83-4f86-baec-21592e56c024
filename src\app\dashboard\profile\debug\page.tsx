import { requireAuth, getUserProfile } from '@/lib/auth'

export default async function ProfileDebugPage() {
  console.log('=== Profile Debug Page ===')
  
  try {
    const user = await requireAuth()
    console.log('User authenticated:', {
      id: user.id,
      email: user.email,
      metadata: user.user_metadata
    })

    const profile = await getUserProfile()
    console.log('Profile data:', profile)

    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Profile Debug</h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold">User Info:</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm">
              {JSON.stringify({
                id: user.id,
                email: user.email,
                metadata: user.user_metadata
              }, null, 2)}
            </pre>
          </div>

          <div>
            <h2 className="text-lg font-semibold">Profile Info:</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm">
              {JSON.stringify(profile, null, 2)}
            </pre>
          </div>

          {!profile && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>Warning:</strong> No profile found for this user!
            </div>
          )}
        </div>
      </div>
    )
  } catch (error) {
    console.error('Debug page error:', error)
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
        <pre className="bg-red-100 p-4 rounded text-sm">
          {error instanceof Error ? error.message : 'Unknown error'}
        </pre>
      </div>
    )
  }
}
