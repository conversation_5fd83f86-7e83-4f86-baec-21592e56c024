import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DailyClosingForm } from '@/components/dashboard/DailyClosingForm'

export default async function DailyClosingPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Use fallback values similar to other pages
  const displayProfile = profile || {
    id: user.id,
    email: user.email || '',
    full_name: user.user_metadata?.full_name || null,
    phone: user.user_metadata?.phone || null,
    role: 'sales_employee' as const,
    role_level: 4 as const,
    area_id: null,
    team_id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }

  return (
    <DashboardLayout user={{
      name: displayProfile.full_name || user.email,
      email: user.email,
      role: displayProfile.role
    }}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">التقفيل اليومي</h1>
        </div>

        <DailyClosingForm user={displayProfile} />
      </div>
    </DashboardLayout>
  )
}
