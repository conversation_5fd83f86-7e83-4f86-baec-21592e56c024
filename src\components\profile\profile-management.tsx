'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Edit, Save, X, Mail, Phone, Calendar, MapPin, Users, User } from 'lucide-react'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'
import { createClient } from '@/lib/supabase/client'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: string
  role_level: number
  area_id: string | null
  team_id: string | null
  created_at: string
  updated_at: string
  team?: {
    name: string
    area: {
      name: string
    }
  } | null
  area?: {
    name: string
  } | null
}

interface ProfileManagementProps {
  profile: Profile
}

export function ProfileManagement({ profile: initialProfile }: ProfileManagementProps) {
  const router = useRouter()
  const [profile, setProfile] = useState(initialProfile)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [editForm, setEditForm] = useState({
    full_name: profile.full_name || '',
    phone: profile.phone || ''
  })

  const supabase = createClient()

  const handleEdit = () => {
    setIsEditing(true)
    setError('')
    setSuccess('')
    setEditForm({
      full_name: profile.full_name || '',
      phone: profile.phone || ''
    })
  }

  const handleCancel = () => {
    setIsEditing(false)
    setError('')
    setSuccess('')
    setEditForm({
      full_name: profile.full_name || '',
      phone: profile.phone || ''
    })
  }

  const handleSave = async () => {
    if (!editForm.full_name.trim()) {
      setError('الاسم الكامل مطلوب')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: editForm.full_name.trim(),
          phone: editForm.phone.trim() || null
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء تحديث الملف الشخصي')
      }

      setProfile(data)
      setIsEditing(false)
      setSuccess('تم تحديث الملف الشخصي بنجاح')

      // Refresh the page to update the layout with new name
      router.refresh()
    } catch (err: any) {
      setError(err.message || 'حدث خطأ أثناء تحديث الملف الشخصي')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {success && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <UserAvatar 
                name={profile.full_name || profile.email}
                className="h-16 w-16"
              />
              <div>
                <CardTitle className="text-xl">
                  {profile.full_name || 'مستخدم غير معروف'}
                </CardTitle>
                <CardDescription className="flex items-center gap-2 mt-1">
                  <Badge variant={getRoleBadgeVariant(profile.role as any)}>
                    {getRoleArabicName(profile.role as any)}
                  </Badge>
                </CardDescription>
              </div>
            </div>
            
            {!isEditing && (
              <Button onClick={handleEdit} variant="outline" size="sm" className="cursor-pointer">
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {isEditing ? (
            /* Edit Form */
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="full_name">الاسم الكامل *</Label>
                <Input
                  id="full_name"
                  value={editForm.full_name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, full_name: e.target.value }))}
                  placeholder="أدخل الاسم الكامل"
                  className="text-right"
                  dir="rtl"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="phone">رقم الهاتف</Label>
                <Input
                  id="phone"
                  value={editForm.phone}
                  onChange={(e) => setEditForm(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="أدخل رقم الهاتف"
                  className="text-right"
                  dir="rtl"
                />
              </div>

              <div className="flex gap-2 justify-start">
                <Button onClick={handleSave} disabled={loading} className="cursor-pointer">
                  <Save className="h-4 w-4 ml-2" />
                  {loading ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button onClick={handleCancel} variant="outline" disabled={loading} className="cursor-pointer">
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
              </div>
            </div>
          ) : (
            /* View Mode */
            <div className="space-y-4">
              {/* Email */}
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">البريد الإلكتروني</p>
                  <p className="text-sm text-muted-foreground">{profile.email}</p>
                </div>
              </div>

              {/* Phone */}
              {profile.phone && (
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">رقم الهاتف</p>
                    <p className="text-sm text-muted-foreground">{profile.phone}</p>
                  </div>
                </div>
              )}

              {/* Area */}
              {(profile.area?.name || profile.team?.area?.name) && (
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">المنطقة</p>
                    <p className="text-sm text-muted-foreground">
                      {profile.area?.name || profile.team?.area?.name}
                    </p>
                  </div>
                </div>
              )}

              {/* Team */}
              {profile.team?.name && (
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">الفريق</p>
                    <p className="text-sm text-muted-foreground">{profile.team.name}</p>
                  </div>
                </div>
              )}

              <Separator />

              {/* Account Info */}
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">تاريخ الانضمام</p>
                  <p className="text-sm text-muted-foreground">{formatDate(profile.created_at)}</p>
                </div>
              </div>

              {profile.updated_at !== profile.created_at && (
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">آخر تحديث</p>
                    <p className="text-sm text-muted-foreground">{formatDate(profile.updated_at)}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
