import { redirect } from 'next/navigation'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Mail, User, Calendar, MapPin, Users, Phone } from 'lucide-react'
import { getRoleArabicName } from '@/lib/roles'

interface ProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ProfilePage({ params }: ProfilePageProps) {
  const { id } = await params
  const cookieStore = await cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Get current user profile
  const { data: currentUserProfile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!currentUserProfile) {
    redirect('/login')
  }

  // Get target user profile
  const { data: targetProfile } = await supabase
    .from('profiles')
    .select(`
      *,
      team:teams!profiles_team_id_fkey(name, area:areas!teams_area_id_fkey(name)),
      area:areas!profiles_area_id_fkey(name)
    `)
    .eq('id', id)
    .single()

  if (!targetProfile) {
    redirect('/dashboard')
  }

  return (
    <DashboardLayout user={{
      name: currentUserProfile.full_name || user.email,
      email: currentUserProfile.email,
      role: currentUserProfile.role
    }}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-3xl font-bold">الملف الشخصي</h2>
          <p className="text-muted-foreground mt-2">
            معلومات المستخدم
          </p>
        </div>

        {/* Profile Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <UserAvatar
                name={targetProfile.full_name || targetProfile.email}
                className="h-16 w-16"
              />
              <div>
                <CardTitle className="text-xl">
                  {targetProfile.full_name || 'مستخدم غير معروف'}
                </CardTitle>
                <CardDescription className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary">
                    {getRoleArabicName(targetProfile.role)}
                  </Badge>
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Email */}
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">البريد الإلكتروني</p>
                <p className="text-sm text-muted-foreground">{targetProfile.email}</p>
              </div>
            </div>

            {/* Phone */}
            {targetProfile.phone && (
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">رقم الهاتف</p>
                  <p className="text-sm text-muted-foreground">{targetProfile.phone}</p>
                </div>
              </div>
            )}

            {/* Team */}
            {targetProfile.team && (
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">الفريق</p>
                  <p className="text-sm text-muted-foreground">{targetProfile.team.name}</p>
                </div>
              </div>
            )}

            {/* Area */}
            {(targetProfile.area || targetProfile.team?.area) && (
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">المنطقة</p>
                  <p className="text-sm text-muted-foreground">
                    {targetProfile.area?.name || targetProfile.team?.area?.name}
                  </p>
                </div>
              </div>
            )}

            {/* Join Date */}
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">تاريخ الانضمام</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(targetProfile.created_at).toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>

            {/* Last Updated */}
            {targetProfile.updated_at !== targetProfile.created_at && (
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">آخر تحديث</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(targetProfile.updated_at).toLocaleDateString('ar-SA', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
