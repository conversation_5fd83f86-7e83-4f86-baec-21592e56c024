# خطة اختبار نظام الأدوار الهرمي - سحابة المدينة

## نظرة عامة
هذا المستند يحتوي على خطة شاملة لاختبار نظام الأدوار الهرمي الجديد المكون من 4 مستويات:

1. **مدير النظام** (system_admin) - المستوى 1 (أعلى صلاحيات)
2. **مدير منطقة** (area_manager) - المستوى 2
3. **مدير فريق** (team_manager) - المستوى 3  
4. **موظف مبيعات** (sales_employee) - المستوى 4 (أقل صلاحيات)

## الاختبارات المطلوبة

### 1. اختبار قاعدة البيانات

#### 1.1 تشغيل Migration
```sql
-- تشغيل migration الجديد
-- ملف: supabase/migrations/003_update_role_hierarchy.sql
```

**النتائج المتوقعة:**
- [ ] تم إضافة عمود `role_level` بنجاح
- [ ] تم تحديث عمود `role` ليدعم الأدوار الجديدة
- [ ] تم تحديث المستخدمين الحاليين:
  - المدراء السابقون → `system_admin` (level 1)
  - المستخدمون العاديون → `sales_employee` (level 4)
- [ ] تم إنشاء الدوال المساعدة بنجاح
- [ ] تم تحديث سياسات RLS

#### 1.2 اختبار القيود (Constraints)
```sql
-- اختبار إدراج دور غير صحيح
INSERT INTO profiles (id, email, role, role_level) 
VALUES ('test-id', '<EMAIL>', 'invalid_role', 1);
-- يجب أن يفشل

-- اختبار عدم تطابق الدور والمستوى
INSERT INTO profiles (id, email, role, role_level) 
VALUES ('test-id', '<EMAIL>', 'system_admin', 4);
-- يجب أن يفشل
```

### 2. اختبار API Routes

#### 2.1 اختبار إنشاء المستخدمين
```bash
# اختبار إنشاء مستخدم بدور مدير منطقة
curl -X POST /api/admin/create-user \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "full_name": "مدير منطقة تجريبي",
    "role": "area_manager"
  }'
```

**النتائج المتوقعة:**
- [ ] مدير النظام يمكنه إنشاء جميع الأدوار
- [ ] يتم تعيين `role_level` تلقائياً حسب الدور
- [ ] رفض الأدوار غير الصحيحة

#### 2.2 اختبار تحديث المستخدمين
```bash
# اختبار تحديث دور مستخدم
curl -X PUT /api/admin/update-user \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-id",
    "role": "team_manager"
  }'
```

### 3. اختبار المصادقة والتفويض

#### 3.1 اختبار الوصول للصفحات
| الدور | /admin | /dashboard | النتيجة المتوقعة |
|-------|--------|------------|------------------|
| system_admin | ✅ | ✅ | وصول كامل |
| area_manager | ❌ | ✅ | وصول للوحة التحكم فقط |
| team_manager | ❌ | ✅ | وصول للوحة التحكم فقط |
| sales_employee | ❌ | ✅ | وصول للوحة التحكم فقط |

#### 3.2 اختبار Middleware
- [ ] إعادة توجيه المستخدمين غير المصرح لهم
- [ ] إعادة توجيه صحيحة بعد تسجيل الدخول حسب الدور

### 4. اختبار واجهة المستخدم

#### 4.1 اختبار الشريط الجانبي
**مدير النظام:**
- [ ] يظهر "مدير النظام" في الرأس
- [ ] يظهر قائمة إدارة المستخدمين
- [ ] يظهر جميع خيارات الإدارة

**مدير منطقة:**
- [ ] يظهر "مدير منطقة" في الرأس
- [ ] يظهر قائمة إدارة الفرق
- [ ] يظهر تقارير المنطقة

**مدير فريق:**
- [ ] يظهر "مدير فريق" في الرأس
- [ ] يظهر قائمة فريقي
- [ ] يظهر تقارير الفريق

**موظف مبيعات:**
- [ ] يظهر "موظف مبيعات" في الرأس
- [ ] يظهر المشاريع الشخصية فقط
- [ ] لا يظهر خيارات الإدارة

#### 4.2 اختبار إدارة المستخدمين
- [ ] مدير النظام يرى جميع المستخدمين
- [ ] عرض الأدوار بالأسماء العربية الصحيحة
- [ ] ألوان الشارات (Badges) صحيحة حسب الدور
- [ ] قائمة الأدوار في النماذج تظهر الأدوار المسموحة فقط

#### 4.3 اختبار النماذج
**إضافة مستخدم:**
- [ ] قائمة الأدوار تظهر الأدوار المسموحة للمستخدم الحالي
- [ ] الأسماء العربية صحيحة
- [ ] الدور الافتراضي هو "موظف مبيعات"

**تعديل مستخدم:**
- [ ] نفس قواعد إضافة المستخدم
- [ ] عرض الدور الحالي بشكل صحيح

### 5. اختبار منطق الأدوار

#### 5.1 اختبار دوال المساعدة
```typescript
// اختبار hasHigherOrEqualRole
expect(hasHigherOrEqualRole('system_admin', 'area_manager')).toBe(true)
expect(hasHigherOrEqualRole('sales_employee', 'system_admin')).toBe(false)

// اختبار canManageUser
expect(canManageUser('area_manager', 'sales_employee')).toBe(true)
expect(canManageUser('sales_employee', 'area_manager')).toBe(false)

// اختبار getAssignableRoles
expect(getAssignableRoles('system_admin')).toContain('area_manager')
expect(getAssignableRoles('team_manager')).not.toContain('system_admin')
```

#### 5.2 اختبار AuthProvider
```typescript
// اختبار الخصائص الجديدة
const { isSystemAdmin, isAreaManagerOrHigher, hasRole } = useAuth()

expect(isSystemAdmin).toBe(true) // للمدير
expect(isAreaManagerOrHigher).toBe(true) // لمدير المنطقة وأعلى
expect(hasRole('team_manager')).toBe(true) // للأدوار المساوية وأعلى
```

### 6. اختبار الأمان

#### 6.1 اختبار RLS Policies
```sql
-- اختبار أن مدير الفريق لا يمكنه رؤية بيانات مدير النظام
SET ROLE authenticated;
SET request.jwt.claims TO '{"sub": "team-manager-id"}';

SELECT * FROM profiles WHERE role = 'system_admin';
-- يجب أن يعيد نتائج فارغة
```

#### 6.2 اختبار API Security
- [ ] رفض الطلبات من مستخدمين غير مصرح لهم
- [ ] التحقق من الأدوار في جميع API routes
- [ ] منع تصعيد الصلاحيات

### 7. اختبار الأداء

#### 7.1 اختبار استعلامات قاعدة البيانات
- [ ] استعلامات الأدوار سريعة ومحسنة
- [ ] فهارس مناسبة على أعمدة الأدوار
- [ ] عدم تأثير سلبي على الأداء

### 8. اختبار التوافق مع الأنظمة الموجودة

#### 8.1 اختبار البيانات الموجودة
- [ ] تحديث المستخدمين الحاليين بنجاح
- [ ] عدم فقدان أي بيانات
- [ ] استمرار عمل الوظائف الموجودة

#### 8.2 اختبار التراجع (Rollback)
- [ ] إمكانية التراجع عن التغييرات إذا لزم الأمر
- [ ] نسخ احتياطية من البيانات

## تشغيل الاختبارات

### 1. اختبارات قاعدة البيانات
```bash
# تشغيل migration
supabase db reset
supabase migration up
```

### 2. اختبارات التطبيق
```bash
# تشغيل التطبيق
npm run dev

# اختبار يدوي للواجهات
# فتح المتصفح على http://localhost:3000
```

### 3. اختبارات API
```bash
# استخدام Postman أو curl لاختبار API endpoints
# مع مستخدمين من أدوار مختلفة
```

## معايير النجاح

- [ ] جميع migrations تعمل بدون أخطاء
- [ ] جميع API routes تعمل مع الأدوار الجديدة
- [ ] واجهة المستخدم تعرض المحتوى المناسب لكل دور
- [ ] الأمان محفوظ ولا يمكن تجاوز الصلاحيات
- [ ] الأداء لم يتأثر سلبياً
- [ ] البيانات الموجودة محفوظة ومحدثة بشكل صحيح

## الإبلاغ عن المشاكل

عند العثور على مشكلة، يرجى تسجيل:
1. خطوات إعادة الإنتاج
2. النتيجة المتوقعة
3. النتيجة الفعلية
4. لقطات شاشة إن أمكن
5. رسائل الخطأ كاملة
