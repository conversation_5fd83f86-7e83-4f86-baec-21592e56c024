import { requireSystemAdmin } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { UserProfilePage } from '@/components/admin/UserProfilePage'
import { notFound } from 'next/navigation'

interface UserProfilePageProps {
  params: {
    id: string
  }
}

export default async function AdminUserProfilePage({ params }: UserProfilePageProps) {
  const profile = await requireSystemAdmin()
  
  // Validate that the ID is a valid UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(params.id)) {
    notFound()
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: profile.role // Use actual role from database
    }}>
      <UserProfilePage userId={params.id} />
    </DashboardLayout>
  )
}
