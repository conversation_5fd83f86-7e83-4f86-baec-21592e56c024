'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ShoppingCart, CheckCircle, AlertTriangle, Trash2, Plus } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { SalesConfirmationDialog } from './SalesConfirmationDialog'
import type { Database } from '@/lib/supabase'

type DailyClosing = Database['public']['Tables']['daily_closings']['Row']
type Package = Database['public']['Tables']['packages']['Row']
type SalesRecord = Database['public']['Tables']['sales_records']['Row']

interface SalesSectionProps {
  dailyClosing: DailyClosing
  onUpdate: () => void
  disabled?: boolean
}

interface SaleItem {
  package_id: string
  quantity: number
  unit_price: number
  total_amount: number
}

export function SalesSection({ dailyClosing, onUpdate, disabled }: SalesSectionProps) {
  const [packages, setPackages] = useState<Package[]>([])
  const [salesRecords, setSalesRecords] = useState<SalesRecord[]>([])
  const [saleItems, setSaleItems] = useState<SaleItem[]>([])
  const [totalSalesAmount, setTotalSalesAmount] = useState(0)
  const [cashDelivered, setCashDelivered] = useState(dailyClosing.cash_delivered || 0)
  const [advancesAmount, setAdvancesAmount] = useState(dailyClosing.advances_amount || 0)
  const [priceBreaksAmount, setPriceBreaksAmount] = useState(dailyClosing.price_breaks_amount || 0)
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const supabase = createClient()

  useEffect(() => {
    fetchPackages()
    fetchSalesRecords()
  }, [dailyClosing.id])

  useEffect(() => {
    calculateTotalSales()
  }, [saleItems])

  const fetchPackages = async () => {
    try {
      const { data, error } = await supabase
        .from('packages')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      setPackages(data || [])
    } catch (error) {
      console.error('Error fetching packages:', error)
    }
  }

  const fetchSalesRecords = async () => {
    try {
      const { data, error } = await supabase
        .from('sales_records')
        .select('*')
        .eq('daily_closing_id', dailyClosing.id)

      if (error) throw error
      
      if (data && data.length > 0) {
        setSalesRecords(data)
        const items = data.map(record => ({
          package_id: record.package_id,
          quantity: record.quantity,
          unit_price: record.unit_price,
          total_amount: record.total_amount
        }))
        setSaleItems(items)
      }
    } catch (error) {
      console.error('Error fetching sales records:', error)
    }
  }

  const calculateTotalSales = () => {
    const total = saleItems.reduce((sum, item) => sum + item.total_amount, 0)
    setTotalSalesAmount(total)
  }

  const addSaleItem = () => {
    setSaleItems([...saleItems, {
      package_id: '',
      quantity: 1,
      unit_price: 0,
      total_amount: 0
    }])
  }

  const removeSaleItem = (index: number) => {
    const newItems = saleItems.filter((_, i) => i !== index)
    setSaleItems(newItems)
  }

  const updateSaleItem = (index: number, field: keyof SaleItem, value: any) => {
    const newItems = [...saleItems]
    newItems[index] = { ...newItems[index], [field]: value }

    // Auto-calculate total when quantity changes
    if (field === 'quantity') {
      newItems[index].total_amount = newItems[index].quantity * newItems[index].unit_price
    }

    // Auto-fill unit price when package is selected
    if (field === 'package_id') {
      const selectedPackage = packages.find(p => p.id === value)
      if (selectedPackage) {
        newItems[index].unit_price = selectedPackage.price
        newItems[index].total_amount = newItems[index].quantity * selectedPackage.price
      }
    }

    setSaleItems(newItems)
  }

  const getPackageName = (packageId: string) => {
    const pkg = packages.find(p => p.id === packageId)
    return pkg?.name || 'غير محدد'
  }

  const validateSales = () => {
    // Check if all items have valid data
    for (const item of saleItems) {
      if (!item.package_id || item.quantity <= 0 || item.unit_price <= 0) {
        return 'يرجى التأكد من إدخال جميع بيانات المبيعات بشكل صحيح'
      }
    }

    // Check if cash delivered doesn't exceed total sales
    if (cashDelivered > totalSalesAmount) {
      return 'المبلغ المسلم لا يمكن أن يكون أكبر من إجمالي المبيعات'
    }

    return null
  }

  const calculateDeficit = () => {
    return Math.max(0, totalSalesAmount - cashDelivered - advancesAmount - priceBreaksAmount)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateSales()
    if (validationError) {
      setError(validationError)
      return
    }

    // Show confirmation dialog instead of directly submitting
    setShowConfirmationDialog(true)
  }

  const handleConfirmSubmit = async () => {
    setLoading(true)
    setError('')

    const deficit = calculateDeficit()

    try {
      // Delete existing sales records
      await supabase
        .from('sales_records')
        .delete()
        .eq('daily_closing_id', dailyClosing.id)

      // Insert new sales records
      if (saleItems.length > 0) {
        const salesData = saleItems.map(item => ({
          daily_closing_id: dailyClosing.id,
          package_id: item.package_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_amount: item.total_amount
        }))

        const { error: salesError } = await supabase
          .from('sales_records')
          .insert(salesData)

        if (salesError) throw salesError
      }

      // Update daily closing
      const { error: updateError } = await supabase
        .from('daily_closings')
        .update({
          sales_submitted: true,
          total_sales_amount: totalSalesAmount,
          cash_delivered: cashDelivered,
          advances_amount: advancesAmount,
          price_breaks_amount: priceBreaksAmount,
          cash_confirmed: true, // Automatically set to true since confirmation is now in popup
          deficit_amount: deficit,
          updated_at: new Date().toISOString()
        })
        .eq('id', dailyClosing.id)

      if (updateError) throw updateError

      setShowConfirmationDialog(false)
      onUpdate()
    } catch (error: any) {
      console.error('Error submitting sales:', error)
      setError('حدث خطأ في حفظ بيانات المبيعات')
    } finally {
      setLoading(false)
    }
  }

  const deficit = calculateDeficit()

  return (
    <>
      <Card className={disabled ? 'opacity-50' : ''}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          تسجيل المبيعات
          {dailyClosing.sales_submitted && (
            <CheckCircle className="h-5 w-5 text-green-600" />
          )}
        </CardTitle>
        <CardDescription>
          سجل مبيعاتك اليومية والمبلغ المسلم
          {disabled && (
            <Badge variant="secondary" className="mr-2">
              يجب تسجيل الحضور أولاً
            </Badge>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6" dir="rtl">
          {/* Sales Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">الباقات المباعة</Label>
              {!disabled && !dailyClosing.sales_submitted && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addSaleItem}
                >
                  <Plus className="h-4 w-4 ml-1" />
                  إضافة باقة
                </Button>
              )}
            </div>

            {saleItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لم يتم إضافة أي مبيعات بعد
              </div>
            ) : (
              <div className="space-y-3">
                {saleItems.map((item, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-4 border rounded-lg">
                    <div>
                      <Label className="text-sm">الباقة</Label>
                      <Select
                        value={item.package_id}
                        onValueChange={(value) => updateSaleItem(index, 'package_id', value)}
                        disabled={disabled || dailyClosing.sales_submitted}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الباقة" />
                        </SelectTrigger>
                        <SelectContent>
                          {packages.map(pkg => (
                            <SelectItem key={pkg.id} value={pkg.id}>
                              {pkg.name} - {pkg.price} ر.س
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm">الكمية</Label>
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => updateSaleItem(index, 'quantity', parseInt(e.target.value) || 0)}
                        disabled={disabled || dailyClosing.sales_submitted}
                      />
                    </div>

                    <div>
                      <Label className="text-sm">سعر الوحدة</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={item.unit_price}
                        disabled
                        className="bg-muted"
                      />
                    </div>

                    <div>
                      <Label className="text-sm">المجموع</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={item.total_amount}
                        disabled
                        className="bg-muted"
                      />
                    </div>

                    {!disabled && !dailyClosing.sales_submitted && (
                      <div className="flex items-end">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeSaleItem(index)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Total Sales */}
          <div className="bg-muted p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium">إجمالي المبيعات:</span>
              <span className="text-lg font-bold">{totalSalesAmount.toFixed(2)} ر.س</span>
            </div>
          </div>

          {/* Cash Delivered */}
          <div className="space-y-2">
            <Label htmlFor="cashDelivered">المبلغ المسلم (نقداً)</Label>
            <Input
              id="cashDelivered"
              type="number"
              step="0.01"
              min="0"
              max={totalSalesAmount || undefined}
              value={cashDelivered}
              onChange={(e) => setCashDelivered(parseFloat(e.target.value) || 0)}
              disabled={disabled || dailyClosing.sales_submitted}
              placeholder="أدخل المبلغ المسلم"
            />
          </div>

          {/* Advances Amount */}
          <div className="space-y-2">
            <Label htmlFor="advancesAmount">السلف</Label>
            <Input
              id="advancesAmount"
              type="number"
              step="0.01"
              min="0"
              value={advancesAmount}
              onChange={(e) => setAdvancesAmount(parseFloat(e.target.value) || 0)}
              disabled={disabled || dailyClosing.sales_submitted}
              placeholder="أدخل قيمة السلف"
            />
          </div>

          {/* Price Breaks Amount */}
          <div className="space-y-2">
            <Label htmlFor="priceBreaksAmount">كسر السعر</Label>
            <Input
              id="priceBreaksAmount"
              type="number"
              step="0.01"
              min="0"
              value={priceBreaksAmount}
              onChange={(e) => setPriceBreaksAmount(parseFloat(e.target.value) || 0)}
              disabled={disabled || dailyClosing.sales_submitted}
              placeholder="أدخل قيمة كسر السعر"
            />
          </div>

          {/* Cash Delivered Exceeds Total Warning */}
          {cashDelivered > totalSalesAmount && totalSalesAmount > 0 && (
            <div className="flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
              <AlertTriangle className="h-4 w-4" />
              <span>خطأ: المبلغ المسلم ({cashDelivered.toFixed(2)} ر.س) أكبر من إجمالي المبيعات ({totalSalesAmount.toFixed(2)} ر.س)</span>
            </div>
          )}

          {/* Deficit Warning */}
          {deficit > 0 && cashDelivered <= totalSalesAmount && (
            <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md">
              <AlertTriangle className="h-4 w-4" />
              <span>تنبيه: هناك عجز بقيمة {deficit.toFixed(2)} ر.س</span>
            </div>
          )}

          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}

          {dailyClosing.sales_submitted ? (
            <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle className="h-4 w-4" />
              <span>تم تسجيل المبيعات بنجاح</span>
            </div>
          ) : (
            <Button 
              type="submit" 
              disabled={disabled || loading || saleItems.length === 0}
              className="w-full"
            >
              {loading ? 'جاري الحفظ...' : 'تسجيل المبيعات'}
            </Button>
          )}
        </form>
      </CardContent>
      </Card>

      {/* Sales Confirmation Dialog */}
      <SalesConfirmationDialog
        open={showConfirmationDialog}
        onOpenChange={setShowConfirmationDialog}
        onConfirm={handleConfirmSubmit}
        saleItems={saleItems}
        totalSalesAmount={totalSalesAmount}
        cashDelivered={cashDelivered}
        advancesAmount={advancesAmount}
        priceBreaksAmount={priceBreaksAmount}
        getPackageName={getPackageName}
        loading={loading}
      />
    </>
  )
}
