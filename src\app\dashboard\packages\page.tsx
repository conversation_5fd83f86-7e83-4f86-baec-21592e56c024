import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { PackageManagement } from '@/components/admin/PackageManagement'
import { redirect } from 'next/navigation'
import { isSystemAdmin } from '@/lib/roles'

export default async function PackagesPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Check if user is system admin
  if (!profile || !isSystemAdmin(profile.role)) {
    redirect('/unauthorized')
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">إدارة الباقات</h2>
          <p className="text-muted-foreground mt-2">
            إدارة باقات الخدمات المتاحة في النظام
          </p>
        </div>

        <PackageManagement />
      </div>
    </DashboardLayout>
  )
}
