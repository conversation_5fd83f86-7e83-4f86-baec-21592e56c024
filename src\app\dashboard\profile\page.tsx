import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ProfileManagement } from '@/components/profile/profile-management'
import { createClient } from '@/lib/supabase/server'

export default async function ProfilePage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Use fallback values similar to main dashboard page
  const displayProfile = profile || {
    id: user.id,
    email: user.email || '',
    full_name: user.user_metadata?.full_name || null,
    phone: user.user_metadata?.phone || null,
    role: 'sales_employee' as const,
    role_level: 4 as const,
    area_id: null,
    team_id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    team: null,
    area: null
  }

  return (
    <DashboardLayout user={{
      name: displayProfile.full_name || user.email,
      email: user.email,
      role: displayProfile.role
    }}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-3xl font-bold">الملف الشخصي</h2>
          <p className="text-muted-foreground mt-2">
            إدارة معلوماتك الشخصية
          </p>
        </div>

        {/* Profile Management Component */}
        <ProfileManagement profile={displayProfile} />
      </div>
    </DashboardLayout>
  )
}
