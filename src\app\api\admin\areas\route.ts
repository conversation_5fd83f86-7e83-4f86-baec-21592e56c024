import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET - List all areas
export async function GET() {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    // Get all areas with manager information
    const { data: areas, error } = await supabaseAdmin
      .from('areas')
      .select(`
        *,
        manager:profiles!manager_id(id, full_name, email),
        teams(id, name, manager_id)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching areas:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب المناطق' },
        { status: 500 }
      )
    }

    return NextResponse.json({ areas })

  } catch (error: any) {
    console.error('Areas API error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - Create new area
export async function POST(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { name, description, manager_id } = await request.json()

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'اسم المنطقة مطلوب' },
        { status: 400 }
      )
    }

    // Validate manager if provided
    if (manager_id) {
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('profiles')
        .select('id, role')
        .eq('id', manager_id)
        .single()

      if (managerError || !manager) {
        return NextResponse.json(
          { error: 'مدير المنطقة غير موجود' },
          { status: 400 }
        )
      }

      // Check if manager has appropriate role
      if (!['system_admin', 'area_manager'].includes(manager.role)) {
        return NextResponse.json(
          { error: 'المستخدم المحدد لا يملك صلاحية إدارة المناطق' },
          { status: 400 }
        )
      }
    }

    // Create area
    const { data: area, error } = await supabaseAdmin
      .from('areas')
      .insert({
        name: name.trim(),
        description: description?.trim() || null,
        manager_id: manager_id || null
      })
      .select(`
        *,
        manager:profiles!manager_id(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error creating area:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء المنطقة' },
        { status: 500 }
      )
    }

    // Update manager's area_id if manager was assigned
    if (manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ area_id: area.id })
        .eq('id', manager_id)
    }

    return NextResponse.json({ area })

  } catch (error: any) {
    console.error('Create area error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
