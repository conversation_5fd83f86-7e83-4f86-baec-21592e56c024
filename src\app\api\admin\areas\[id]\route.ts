import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET - Get specific area
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف المنطقة غير صحيح' },
        { status: 400 }
      )
    }

    // Get area with manager and teams information
    const { data: area, error } = await supabaseAdmin
      .from('areas')
      .select(`
        *,
        manager:profiles!areas_manager_id_fkey(id, full_name, email, role),
        teams(
          id,
          name,
          description,
          manager_id,
          manager:profiles!teams_manager_id_fkey(id, full_name, email)
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'المنطقة غير موجودة' },
          { status: 404 }
        )
      }
      console.error('Error fetching area:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات المنطقة' },
        { status: 500 }
      )
    }

    return NextResponse.json({ area })

  } catch (error: any) {
    console.error('Get area error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - Update area
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params
    const { name, description, manager_id } = await request.json()

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف المنطقة غير صحيح' },
        { status: 400 }
      )
    }

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'اسم المنطقة مطلوب' },
        { status: 400 }
      )
    }

    // Get current area data
    const { data: currentArea, error: currentError } = await supabaseAdmin
      .from('areas')
      .select('manager_id')
      .eq('id', id)
      .single()

    if (currentError) {
      return NextResponse.json(
        { error: 'المنطقة غير موجودة' },
        { status: 404 }
      )
    }

    // Validate new manager if provided
    if (manager_id) {
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('profiles')
        .select('id, role')
        .eq('id', manager_id)
        .single()

      if (managerError || !manager) {
        return NextResponse.json(
          { error: 'مدير المنطقة غير موجود' },
          { status: 400 }
        )
      }

      // Check if manager has appropriate role
      if (!['system_admin', 'area_manager'].includes(manager.role)) {
        return NextResponse.json(
          { error: 'المستخدم المحدد لا يملك صلاحية إدارة المناطق' },
          { status: 400 }
        )
      }
    }

    // Update area
    const { data: area, error } = await supabaseAdmin
      .from('areas')
      .update({
        name: name.trim(),
        description: description?.trim() || null,
        manager_id: manager_id || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        manager:profiles!manager_id(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error updating area:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث المنطقة' },
        { status: 500 }
      )
    }

    // Update manager assignments
    // Remove area_id from old manager if changed
    if (currentArea.manager_id && currentArea.manager_id !== manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ area_id: null })
        .eq('id', currentArea.manager_id)
    }

    // Set area_id for new manager
    if (manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ area_id: area.id })
        .eq('id', manager_id)
    }

    return NextResponse.json({ area })

  } catch (error: any) {
    console.error('Update area error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - Delete area
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف المنطقة غير صحيح' },
        { status: 400 }
      )
    }

    // Check if area has teams
    const { data: teams, error: teamsError } = await supabaseAdmin
      .from('teams')
      .select('id')
      .eq('area_id', id)

    if (teamsError) {
      console.error('Error checking teams:', teamsError)
      return NextResponse.json(
        { error: 'حدث خطأ في التحقق من الفرق' },
        { status: 500 }
      )
    }

    if (teams && teams.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف المنطقة لأنها تحتوي على فرق. يرجى حذف الفرق أولاً.' },
        { status: 400 }
      )
    }

    // Get area manager to update their profile
    const { data: area, error: areaError } = await supabaseAdmin
      .from('areas')
      .select('manager_id')
      .eq('id', id)
      .single()

    if (areaError && areaError.code !== 'PGRST116') {
      console.error('Error fetching area:', areaError)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات المنطقة' },
        { status: 500 }
      )
    }

    // Delete area
    const { error } = await supabaseAdmin
      .from('areas')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting area:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف المنطقة' },
        { status: 500 }
      )
    }

    // Remove area_id from manager's profile
    if (area?.manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ area_id: null })
        .eq('id', area.manager_id)
    }

    return NextResponse.json({ success: true })

  } catch (error: any) {
    console.error('Delete area error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
