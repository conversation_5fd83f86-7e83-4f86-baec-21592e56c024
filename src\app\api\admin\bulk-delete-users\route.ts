import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { isSystemAdmin } from '@/lib/roles'

export async function DELETE(request: Request) {
  try {
    // Check authentication
    const cookieStore = await cookies()
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Ignore
            }
          },
        },
      }
    )

    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || !isSystemAdmin(profile.role)) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول لهذه الصفحة' },
        { status: 403 }
      )
    }

    // Get request body
    const { userIds } = await request.json()

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'يجب تحديد مستخدمين للحذف' },
        { status: 400 }
      )
    }

    // Prevent deleting system admins
    const { data: usersToDelete, error: fetchError } = await supabase
      .from('profiles')
      .select('id, role, full_name, email')
      .in('id', userIds)

    if (fetchError) {
      return NextResponse.json(
        { error: 'خطأ في جلب بيانات المستخدمين' },
        { status: 500 }
      )
    }

    // Check if any of the users are system admins
    const systemAdmins = usersToDelete?.filter(user => user.role === 'system_admin') || []
    if (systemAdmins.length > 0) {
      const adminNames = systemAdmins.map(admin => admin.full_name || admin.email).join(', ')
      return NextResponse.json(
        { error: `لا يمكن حذف مديري النظام: ${adminNames}` },
        { status: 400 }
      )
    }

    // Prevent deleting current user
    if (userIds.includes(user.id)) {
      return NextResponse.json(
        { error: 'لا يمكنك حذف حسابك الخاص' },
        { status: 400 }
      )
    }

    // Delete users from profiles table
    const { error: deleteError } = await supabase
      .from('profiles')
      .delete()
      .in('id', userIds)

    if (deleteError) {
      console.error('Error deleting users:', deleteError)
      return NextResponse.json(
        { error: `خطأ في حذف المستخدمين: ${deleteError.message}` },
        { status: 500 }
      )
    }

    // Note: In a production environment, you might also want to:
    // 1. Delete users from Supabase Auth (requires service role key)
    // 2. Clean up related data (assignments, etc.)
    // 3. Log the deletion for audit purposes

    return NextResponse.json({
      message: `تم حذف ${userIds.length} مستخدم بنجاح`,
      deletedCount: userIds.length
    })

  } catch (error: any) {
    console.error('Unexpected error in bulk delete users API:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
