'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Shield, Users, Eye, Edit, Trash2, Plus, Settings, BarChart3, TrendingUp } from 'lucide-react'
import { UserRole, ROLE_HIERARCHY } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'

export function RolesAndPermissions() {
  const [activeTab, setActiveTab] = useState('overview')
  const [userStats, setUserStats] = useState<Record<string, number>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserStats()
  }, [])

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/admin/user-stats')
      if (response.ok) {
        const stats = await response.json()
        setUserStats(stats)
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6" dir="rtl">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="text-right">نظرة عامة</TabsTrigger>
          <TabsTrigger value="hierarchy" className="text-right">الهيكل الهرمي</TabsTrigger>
          <TabsTrigger value="permissions" className="text-right">الصلاحيات</TabsTrigger>
          <TabsTrigger value="statistics" className="text-right">الإحصائيات</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <RolesOverview />
        </TabsContent>

        <TabsContent value="hierarchy" className="space-y-4">
          <RoleHierarchy />
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <PermissionsMatrix />
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <RoleStatistics userStats={userStats} loading={loading} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

function RolesOverview() {
  const roles = Object.entries(ROLE_HIERARCHY)

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {roles.map(([roleKey, roleInfo]) => (
        <Card key={roleKey} className="text-right">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <Badge variant={getRoleBadgeVariant(roleKey as UserRole)}>
                المستوى {roleInfo.level}
              </Badge>
              <Shield className="h-5 w-5 text-muted-foreground" />
            </div>
            <CardTitle className="text-lg">{roleInfo.arabicName}</CardTitle>
            <CardDescription className="text-sm">
              {roleInfo.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-4 w-4" />
                <span>يدير المستويات: {roleInfo.level}-4</span>
              </div>
              <div className="text-xs text-muted-foreground">
                الصلاحيات: {roleInfo.permissions.join('، ')}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function RoleHierarchy() {
  const roles = Object.entries(ROLE_HIERARCHY).sort(([, a], [, b]) => a.level - b.level)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-right">الهيكل الهرمي للأدوار</CardTitle>
        <CardDescription className="text-right">
          يوضح هذا المخطط التسلسل الهرمي للأدوار وعلاقات الإدارة بينها
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {roles.map(([roleKey, roleInfo], index) => (
            <div key={roleKey} className="relative">
              {index > 0 && (
                <div className="absolute right-6 -top-4 w-0.5 h-4 bg-border"></div>
              )}
              <div className="flex items-center gap-4 p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <Shield className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{roleInfo.arabicName}</h3>
                      <Badge variant={getRoleBadgeVariant(roleKey as UserRole)}>
                        المستوى {roleInfo.level}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {roleInfo.description}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {roleInfo.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              {index < roles.length - 1 && (
                <div className="flex items-center justify-center my-2">
                  <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                    يدير ↓
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function PermissionsMatrix() {
  const roles = Object.entries(ROLE_HIERARCHY).sort(([, a], [, b]) => a.level - b.level)
  
  const allPermissions = [
    'إدارة النظام',
    'إدارة المستخدمين',
    'إنشاء المستخدمين',
    'تعديل المستخدمين',
    'حذف المستخدمين',
    'عرض التقارير',
    'إدارة المشاريع',
    'إدارة الفرق',
    'إدارة المبيعات'
  ]

  const getPermissionStatus = (roleKey: string, permission: string) => {
    const roleInfo = ROLE_HIERARCHY[roleKey as UserRole]
    if (!roleInfo) return false
    
    // System admin has all permissions
    if (roleKey === 'system_admin') return true
    
    // Check if permission is in role's permissions array
    return roleInfo.permissions.includes(permission)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-right">مصفوفة الصلاحيات</CardTitle>
        <CardDescription className="text-right">
          جدول يوضح الصلاحيات المتاحة لكل دور في النظام
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-right p-3 font-semibold">الصلاحية</th>
                {roles.map(([roleKey, roleInfo]) => (
                  <th key={roleKey} className="text-center p-3 font-semibold min-w-[120px]">
                    <div className="space-y-1">
                      <div>{roleInfo.arabicName}</div>
                      <Badge variant={getRoleBadgeVariant(roleKey as UserRole)} className="text-xs">
                        المستوى {roleInfo.level}
                      </Badge>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {allPermissions.map((permission) => (
                <tr key={permission} className="border-b hover:bg-muted/50">
                  <td className="text-right p-3 font-medium">{permission}</td>
                  {roles.map(([roleKey]) => (
                    <td key={roleKey} className="text-center p-3">
                      {getPermissionStatus(roleKey, permission) ? (
                        <div className="w-6 h-6 rounded-full bg-green-500 mx-auto flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      ) : (
                        <div className="w-6 h-6 rounded-full bg-gray-300 mx-auto flex items-center justify-center">
                          <span className="text-gray-500 text-xs">✗</span>
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

function RoleStatistics({ userStats, loading }: { userStats: Record<string, number>, loading: boolean }) {
  const roles = Object.entries(ROLE_HIERARCHY)
  const totalUsers = Object.values(userStats).reduce((sum, count) => sum + count, 0)

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-3">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Role Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {roles.map(([roleKey, roleInfo]) => {
          const count = userStats[roleKey] || 0
          const percentage = totalUsers > 0 ? ((count / totalUsers) * 100).toFixed(1) : '0'

          return (
            <Card key={roleKey} className="text-right">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Badge variant={getRoleBadgeVariant(roleKey as UserRole)}>
                    {percentage}%
                  </Badge>
                  <Users className="h-5 w-5 text-muted-foreground" />
                </div>
                <CardTitle className="text-lg">{roleInfo.arabicName}</CardTitle>
                <CardDescription className="text-sm">
                  المستوى {roleInfo.level}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">{count}</div>
                  <div className="text-xs text-muted-foreground">
                    {count === 1 ? 'مستخدم واحد' : count === 2 ? 'مستخدمان' : `${count} مستخدمين`}
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary rounded-full h-2 transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            ملخص الإحصائيات
          </CardTitle>
          <CardDescription className="text-right">
            نظرة عامة على توزيع المستخدمين حسب الأدوار
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary">{totalUsers}</div>
              <div className="text-sm text-muted-foreground">إجمالي المستخدمين</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Object.keys(userStats).length}
              </div>
              <div className="text-sm text-muted-foreground">الأدوار المستخدمة</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">4</div>
              <div className="text-sm text-muted-foreground">إجمالي الأدوار المتاحة</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
