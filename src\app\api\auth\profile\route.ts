import { createClient } from '@/lib/supabase/server'
import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('Profile API called')

    const supabase = await createClient()
    console.log('Supabase client created')

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log('Auth getUser result:', { user: user?.id, error: userError })

    if (userError) {
      console.error('Error getting user:', userError)
      return NextResponse.json({ error: 'Authentication error', details: userError.message }, { status: 401 })
    }

    if (!user) {
      console.error('No user found')
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    console.log('Fetching profile for user:', user.id)

    // Use service role to bypass RLS for this operation
    const supabaseAdmin = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Fetch the user's profile using admin client
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    console.log('Profile fetch result:', { profile: profile?.id, error: profileError })

    if (profileError) {
      console.error('Error fetching profile:', profileError)

      // If profile doesn't exist, create a default one
      if (profileError.code === 'PGRST116') {
        console.log('Profile not found, creating default profile')

        const { data: newProfile, error: createError } = await supabaseAdmin
          .from('profiles')
          .insert({
            id: user.id,
            email: user.email || '',
            full_name: user.user_metadata?.full_name || null,
            phone: user.user_metadata?.phone || null,
            role: 'sales_employee',
            role_level: 4
          })
          .select()
          .single()

        if (createError) {
          console.error('Error creating profile:', createError)
          return NextResponse.json({
            error: 'Failed to create profile',
            details: createError.message
          }, { status: 500 })
        }

        console.log('Profile created successfully:', newProfile)
        return NextResponse.json(newProfile)
      }

      return NextResponse.json({
        error: 'Failed to fetch profile',
        details: profileError.message
      }, { status: 500 })
    }

    console.log('Profile fetched successfully:', profile)
    return NextResponse.json(profile)

  } catch (error) {
    console.error('Unexpected error in profile API:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
