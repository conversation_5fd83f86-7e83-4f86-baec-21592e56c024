"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { SidebarNavigationLink } from "@/components/ui/navigation-link"
import {
  Home,
  Users,
  Settings,
  BarChart3,
  Calendar,
  MessageSquare,
  Bell,
  ChevronLeft,
  User,
  LogOut,
  Cloud,
  ClipboardCheck,
  ShoppingCart,
  MapPin,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { UserRole } from '@/lib/supabase'
import { getRoleArabicName, isSystemAdmin, isAreaManagerOrHigher, isTeamManagerOrHigher } from '@/lib/roles'
import { NavigationErrorBoundary } from "@/components/ui/error-boundary"
import { NavigationTransition } from "@/components/ui/page-transition"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { UserAvatar } from "@/components/ui/user-avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { LogoutButton } from "@/components/ui/logout-button"
import { useAuth } from "@/hooks/useAuth"
import { logoutAction } from "@/app/actions/auth"

// Navigation item types
interface NavigationItem {
  title: string
  url: string
  icon: any
  badge?: string
  items?: {
    title: string
    url: string
  }[]
}

// Navigation data for different user roles
const systemAdminNavigation: NavigationItem[] = [
  {
    title: "الرئيسية",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "إدارة المستخدمين",
    url: "/dashboard/users",
    icon: Users,
    items: [
      {
        title: "جميع المستخدمين",
        url: "/dashboard/users",
      },
      {
        title: "إضافة مستخدم",
        url: "/dashboard/users/new",
      },
      {
        title: "الأدوار والصلاحيات",
        url: "/dashboard/users/roles",
      },
    ],
  },
  {
    title: "إدارة المناطق والفرق",
    url: "/dashboard/areas",
    icon: MapPin,
    items: [
      {
        title: "إدارة المناطق",
        url: "/dashboard/areas",
      },
      {
        title: "إدارة الفرق",
        url: "/dashboard/teams",
      },
      {
        title: "تعيين المستخدمين",
        url: "/dashboard/assign-users",
      },
    ],
  },
  {
    title: "الطلبات",
    url: "/dashboard/tickets",
    icon: MessageSquare,
    items: [
      {
        title: "الطلبات المفتوحة",
        url: "/dashboard/tickets/open",
      },
      {
        title: "جميع الطلبات",
        url: "/dashboard/tickets/all",
      },
    ],
  },
  {
    title: "التقارير",
    url: "/dashboard/admin/reports",
    icon: BarChart3,
    items: [
      {
        title: "تقارير النشاط",
        url: "/dashboard/admin/reports/activity",
      },
      {
        title: "تقارير المستخدمين",
        url: "/dashboard/admin/reports/users",
      },
      {
        title: "إحصائيات النظام",
        url: "/dashboard/admin/reports/system",
      },
    ],
  },

  {
    title: "إدارة الباقات",
    url: "/dashboard/packages",
    icon: ShoppingCart,
  },
  {
    title: "التقويم",
    url: "/dashboard/admin/calendar",
    icon: Calendar,
  },

  {
    title: "الإعدادات",
    url: "/dashboard/admin/settings",
    icon: Settings,
    items: [
      {
        title: "إعدادات النظام",
        url: "/dashboard/admin/settings/system",
      },
      {
        title: "إعدادات الأمان",
        url: "/dashboard/admin/settings/security",
      },
      {
        title: "النسخ الاحتياطي",
        url: "/dashboard/admin/settings/backup",
      },
    ],
  },
]

const areaManagerNavigation: NavigationItem[] = [
  {
    title: "الرئيسية",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "إدارة الفرق",
    url: "/dashboard/teams",
    icon: Users,
    items: [
      {
        title: "جميع الفرق",
        url: "/dashboard/teams",
      },
      {
        title: "إضافة فريق",
        url: "/dashboard/teams/new",
      },
    ],
  },

  {
    title: "الطلبات",
    url: "/dashboard/tickets",
    icon: MessageSquare,
    items: [
      {
        title: "الطلبات المفتوحة",
        url: "/dashboard/tickets/open",
      },
      {
        title: "جميع الطلبات",
        url: "/dashboard/tickets/all",
      },
    ],
  },
  {
    title: "التقارير",
    url: "/dashboard/reports",
    icon: BarChart3,
    items: [
      {
        title: "تقارير المنطقة",
        url: "/dashboard/reports/area",
      },
      {
        title: "تقارير الفرق",
        url: "/dashboard/reports/teams",
      },
    ],
  },
  {
    title: "التقويم",
    url: "/dashboard/calendar",
    icon: Calendar,
  },

]

const teamManagerNavigation: NavigationItem[] = [
  {
    title: "الرئيسية",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "فريقي",
    url: "/dashboard/team",
    icon: Users,
    items: [
      {
        title: "أعضاء الفريق",
        url: "/dashboard/team/members",
      },
      {
        title: "أداء الفريق",
        url: "/dashboard/team/performance",
      },
    ],
  },

  {
    title: "الطلبات",
    url: "/dashboard/tickets",
    icon: MessageSquare,
    items: [
      {
        title: "الطلبات المفتوحة",
        url: "/dashboard/tickets/open",
      },
      {
        title: "جميع الطلبات",
        url: "/dashboard/tickets/all",
      },
    ],
  },
  {
    title: "التقارير",
    url: "/dashboard/reports",
    icon: BarChart3,
    items: [
      {
        title: "تقارير الفريق",
        url: "/dashboard/reports/team",
      },
    ],
  },
  {
    title: "التقويم",
    url: "/dashboard/calendar",
    icon: Calendar,
  },

]

const salesEmployeeNavigation: NavigationItem[] = [
  {
    title: "الرئيسية",
    url: "/dashboard",
    icon: Home,
  },
  {
    title: "التقفيل اليومي",
    url: "/dashboard/daily-closing",
    icon: ClipboardCheck,
  },
  {
    title: "الطلبات",
    url: "/dashboard/tickets",
    icon: MessageSquare,
  },
]

// Function to get navigation based on user role
function getNavigationForRole(role?: string | null) {
  if (!role || typeof role !== 'string') {
    return salesEmployeeNavigation
  }

  switch (role) {
    case 'system_admin':
      return systemAdminNavigation
    case 'area_manager':
      return areaManagerNavigation
    case 'team_manager':
      return teamManagerNavigation
    case 'sales_employee':
    default:
      return salesEmployeeNavigation
  }
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: {
    name?: string
    email?: string
    avatar?: string
    role?: string
  } | null
}

export function AppSidebar({ user, ...props }: AppSidebarProps) {
  const pathname = usePathname()
  const isAdmin = pathname.startsWith('/dashboard/admin')
  const navigation = getNavigationForRole(user?.role)
  const { signOut } = useAuth()
  const [loggingOut, setLoggingOut] = React.useState(false)

  const handleLogout = async () => {
    console.log('Direct logout handler called')
    setLoggingOut(true)

    try {
      console.log('Calling client-side logout...')
      if (signOut) {
        await signOut()
      } else {
        console.warn('signOut not available, forcing redirect')
        window.location.replace('/login')
      }
    } catch (error) {
      console.error('Logout error (forcing redirect):', error)
      // Force redirect even on error
      window.location.replace('/login')
    }

    // Don't set loggingOut to false here since we're redirecting
  }

  return (
    <NavigationErrorBoundary>
      <Sidebar collapsible="icon" side="right" className="transition-all duration-300 ease-in-out" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-2 py-2 min-h-[3rem] group-data-[collapsible=icon]:justify-center">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground shrink-0">
            <Cloud className="size-4" />
          </div>
          <div className="grid flex-1 text-right text-sm leading-tight group-data-[collapsible=icon]:hidden">
            <span className="truncate font-semibold">سحابة المدينة</span>
            <span className="truncate text-xs text-muted-foreground">
              {user?.role ? getRoleArabicName(user.role) : "نظام إدارة العمل"}
            </span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>التنقل الرئيسي</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="group-data-[collapsible=icon]:items-center">
              {navigation.map((item) => {
                const isActive = pathname === item.url || pathname.startsWith(item.url + '/')
                
                if (item.items) {
                  return (
                    <Collapsible key={item.title} asChild defaultOpen={isActive}>
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            isActive={isActive}
                            className="w-full justify-between transition-all duration-200 hover:bg-sidebar-accent/50"
                          >
                            <div className="flex items-center gap-2">
                              <item.icon className="size-4" />
                              <span className="flex-1 text-right">{item.title}</span>
                              {item.badge && (
                                <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                                  {item.badge}
                                </span>
                              )}
                            </div>
                            <ChevronLeft className="size-4 transition-transform group-data-[state=open]:rotate-90" />
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.items.map((subItem) => (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton
                                  asChild
                                  isActive={pathname === subItem.url}
                                >
                                  <SidebarNavigationLink 
                                    href={subItem.url} 
                                    className="text-right"
                                    loadingText={`جاري تحميل ${subItem.title}...`}
                                  >
                                    {subItem.title}
                                  </SidebarNavigationLink>
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            ))}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  )
                }

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <SidebarNavigationLink 
                        href={item.url} 
                        isActive={isActive}
                        loadingText={`جاري تحميل ${item.title}...`}
                      >
                        <item.icon className="size-4" />
                        <span className="flex-1 text-right">{item.title}</span>
                        {item.badge && (
                          <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                            {item.badge}
                          </span>
                        )}
                      </SidebarNavigationLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <UserAvatar
                    src={user?.avatar}
                    alt={user?.name}
                    name={user?.name}
                    size="md"
                    className="rounded-lg"
                    fallbackClassName="rounded-lg"
                  />
                  <div className="grid flex-1 text-right text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.name || user?.email || 'المستخدم'}
                    </span>
                    <span className="truncate text-xs">
                      {user?.role ? getRoleArabicName(user.role) : 'مستخدم'}
                    </span>
                  </div>
                  <ChevronLeft className="mr-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-right text-sm">
                    <UserAvatar
                      src={user?.avatar}
                      alt={user?.name}
                      name={user?.name}
                      size="md"
                      className="rounded-lg"
                      fallbackClassName="rounded-lg"
                    />
                    <div className="grid flex-1 text-right text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user?.name || user?.email || 'المستخدم'}
                      </span>
                      <span className="truncate text-xs">
                        {user?.email}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/profile" className="flex items-center gap-2">
                    <User className="size-4" />
                    الملف الشخصي
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings" className="flex items-center gap-2">
                    <Settings className="size-4" />
                    الإعدادات
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="cursor-pointer"
                  disabled={loggingOut}
                >
                  {loggingOut ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                      جاري تسجيل الخروج...
                    </>
                  ) : (
                    <>
                      <LogOut className="size-4" />
                      تسجيل الخروج
                    </>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
    </NavigationErrorBoundary>
  )
}
