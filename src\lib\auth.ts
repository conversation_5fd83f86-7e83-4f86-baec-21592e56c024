import { createClient } from './supabase/server'
import { redirect } from 'next/navigation'
import { UserRole, RoleLevel } from './supabase'
import { hasHigherOrEqualRole, isSystemAdmin } from './roles'

export async function getUser() {
  const supabase = await createClient()
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return null
  }
  
  return user
}

export async function getUserProfile() {
  const supabase = await createClient()
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  
  if (userError || !user) {
    return null
  }
  
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
  
  if (profileError) {
    return null
  }
  
  return profile
}

export async function requireAuth() {
  const user = await getUser()
  if (!user) {
    redirect('/login')
  }
  return user
}

export async function requireAdmin() {
  const profile = await getUserProfile()
  if (!profile || !isSystemAdmin(profile.role)) {
    redirect('/unauthorized')
  }
  return profile
}

export async function requireRole(requiredRole: UserRole) {
  const profile = await getUserProfile()
  if (!profile || !hasHigherOrEqualRole(profile.role, requiredRole)) {
    redirect('/unauthorized')
  }
  return profile
}

export async function requireRoleLevel(requiredLevel: RoleLevel) {
  const profile = await getUserProfile()
  if (!profile || profile.role_level > requiredLevel) {
    redirect('/unauthorized')
  }
  return profile
}

export async function requireSystemAdmin() {
  return requireRole('system_admin')
}

export async function requireAreaManagerOrHigher() {
  return requireRole('area_manager')
}

export async function requireTeamManagerOrHigher() {
  return requireRole('team_manager')
}

export async function signOut() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  redirect('/login')
}
