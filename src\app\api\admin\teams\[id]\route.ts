import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET - Get specific team
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف الفريق غير صحيح' },
        { status: 400 }
      )
    }

    // Get team with area, manager and members information
    const { data: team, error } = await supabaseAdmin
      .from('teams')
      .select(`
        *,
        area:areas!teams_area_id_fkey(id, name, manager_id),
        manager:profiles!teams_manager_id_fkey(id, full_name, email, role),
        members:profiles!profiles_team_id_fkey(id, full_name, email, role, created_at)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'الفريق غير موجود' },
          { status: 404 }
        )
      }
      console.error('Error fetching team:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات الفريق' },
        { status: 500 }
      )
    }

    return NextResponse.json({ team })

  } catch (error: any) {
    console.error('Get team error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - Update team
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params
    const { name, description, area_id, manager_id } = await request.json()

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف الفريق غير صحيح' },
        { status: 400 }
      )
    }

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'اسم الفريق مطلوب' },
        { status: 400 }
      )
    }

    if (!area_id) {
      return NextResponse.json(
        { error: 'المنطقة مطلوبة' },
        { status: 400 }
      )
    }

    // Get current team data
    const { data: currentTeam, error: currentError } = await supabaseAdmin
      .from('teams')
      .select('manager_id')
      .eq('id', id)
      .single()

    if (currentError) {
      return NextResponse.json(
        { error: 'الفريق غير موجود' },
        { status: 404 }
      )
    }

    // Validate area exists
    const { data: area, error: areaError } = await supabaseAdmin
      .from('areas')
      .select('id, name')
      .eq('id', area_id)
      .single()

    if (areaError || !area) {
      return NextResponse.json(
        { error: 'المنطقة المحددة غير موجودة' },
        { status: 400 }
      )
    }

    // Validate new manager if provided
    if (manager_id) {
      const { data: manager, error: managerError } = await supabaseAdmin
        .from('profiles')
        .select('id, role')
        .eq('id', manager_id)
        .single()

      if (managerError || !manager) {
        return NextResponse.json(
          { error: 'مدير الفريق غير موجود' },
          { status: 400 }
        )
      }

      // Check if manager has appropriate role
      if (!['system_admin', 'area_manager', 'team_manager'].includes(manager.role)) {
        return NextResponse.json(
          { error: 'المستخدم المحدد لا يملك صلاحية إدارة الفرق' },
          { status: 400 }
        )
      }
    }

    // Update team
    const { data: team, error } = await supabaseAdmin
      .from('teams')
      .update({
        name: name.trim(),
        description: description?.trim() || null,
        area_id,
        manager_id: manager_id || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        area:areas(id, name),
        manager:profiles!manager_id(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error updating team:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث الفريق' },
        { status: 500 }
      )
    }

    // Update manager assignments
    // Remove team_id from old manager if changed
    if (currentTeam.manager_id && currentTeam.manager_id !== manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ team_id: null })
        .eq('id', currentTeam.manager_id)
    }

    // Set team_id for new manager
    if (manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ team_id: team.id })
        .eq('id', manager_id)
    }

    return NextResponse.json({ team })

  } catch (error: any) {
    console.error('Update team error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - Delete team
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { id } = await params

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'معرف الفريق غير صحيح' },
        { status: 400 }
      )
    }

    // Check if team has members
    const { data: members, error: membersError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('team_id', id)

    if (membersError) {
      console.error('Error checking members:', membersError)
      return NextResponse.json(
        { error: 'حدث خطأ في التحقق من أعضاء الفريق' },
        { status: 500 }
      )
    }

    if (members && members.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف الفريق لأنه يحتوي على أعضاء. يرجى إزالة الأعضاء أولاً.' },
        { status: 400 }
      )
    }

    // Get team manager to update their profile
    const { data: team, error: teamError } = await supabaseAdmin
      .from('teams')
      .select('manager_id')
      .eq('id', id)
      .single()

    if (teamError && teamError.code !== 'PGRST116') {
      console.error('Error fetching team:', teamError)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات الفريق' },
        { status: 500 }
      )
    }

    // Delete team
    const { error } = await supabaseAdmin
      .from('teams')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting team:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف الفريق' },
        { status: 500 }
      )
    }

    // Remove team_id from manager's profile
    if (team?.manager_id) {
      await supabaseAdmin
        .from('profiles')
        .update({ team_id: null })
        .eq('id', team.manager_id)
    }

    return NextResponse.json({ success: true })

  } catch (error: any) {
    console.error('Delete team error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
