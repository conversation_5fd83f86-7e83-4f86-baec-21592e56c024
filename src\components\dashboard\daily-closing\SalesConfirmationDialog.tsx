'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { AlertTriangle, CheckCircle } from 'lucide-react'

interface SaleItem {
  package_id: string
  quantity: number
  unit_price: number
  total_amount: number
}

interface SalesConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
  saleItems: SaleItem[]
  totalSalesAmount: number
  cashDelivered: number
  advancesAmount: number
  priceBreaksAmount: number
  getPackageName: (packageId: string) => string
  loading?: boolean
}

export function SalesConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  saleItems,
  totalSalesAmount,
  cashDelivered,
  advancesAmount,
  priceBreaksAmount,
  getPackageName,
  loading = false
}: SalesConfirmationDialogProps) {
  const [consentConfirmed, setConsentConfirmed] = useState(false)

  const adjustedDeficit = Math.max(0, totalSalesAmount - cashDelivered - advancesAmount - priceBreaksAmount)

  const handleConfirm = () => {
    if (consentConfirmed) {
      onConfirm()
    }
  }

  const handleClose = () => {
    setConsentConfirmed(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-600" />
            تأكيد بيانات المبيعات
          </DialogTitle>
          <DialogDescription>
            يرجى مراجعة البيانات التالية والموافقة على صحتها قبل التسجيل النهائي
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Sales Summary */}
          <div>
            <h4 className="font-medium mb-3">ملخص المبيعات</h4>
            <div className="space-y-2">
              {saleItems.map((item, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-muted rounded-lg">
                  <div>
                    <span className="font-medium">{getPackageName(item.package_id)}</span>
                    <span className="text-sm text-muted-foreground mr-2">
                      ({item.quantity} × {item.unit_price.toFixed(2)} ر.س)
                    </span>
                  </div>
                  <span className="font-bold">{item.total_amount.toFixed(2)} ر.س</span>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Financial Summary */}
          <div>
            <h4 className="font-medium mb-3">الملخص المالي</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span>إجمالي المبيعات</span>
                <span className="font-bold text-green-600">{totalSalesAmount.toFixed(2)} ر.س</span>
              </div>
              
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span>المبلغ المسلم</span>
                <span className="font-bold text-blue-600">{cashDelivered.toFixed(2)} ر.س</span>
              </div>

              {advancesAmount > 0 && (
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span>السلف</span>
                  <span className="font-bold text-purple-600">-{advancesAmount.toFixed(2)} ر.س</span>
                </div>
              )}

              {priceBreaksAmount > 0 && (
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span>كسر السعر</span>
                  <span className="font-bold text-orange-600">-{priceBreaksAmount.toFixed(2)} ر.س</span>
                </div>
              )}

              <Separator />

              {adjustedDeficit > 0 ? (
                <div className="flex justify-between items-center p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-amber-600" />
                    <span className="text-amber-700">العجز النهائي</span>
                  </div>
                  <span className="font-bold text-amber-600">{adjustedDeficit.toFixed(2)} ر.س</span>
                </div>
              ) : (
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-green-700">لا يوجد عجز</span>
                  </div>
                  <span className="font-bold text-green-600">0.00 ر.س</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Consent Checkbox */}
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <Checkbox
                id="consent"
                checked={consentConfirmed}
                onCheckedChange={(checked) => setConsentConfirmed(checked as boolean)}
                className="mt-1"
              />
              <Label htmlFor="consent" className="text-sm leading-relaxed flex-1">
                أؤكد أن جميع البيانات المدخلة صحيحة ودقيقة، وأتحمل المسؤولية الكاملة عن صحة هذه المعلومات.
                كما أؤكد أن المبالغ المالية المذكورة تتطابق مع الواقع الفعلي للمبيعات والنقد المسلم.
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!consentConfirmed || loading}
            className="min-w-[120px]"
          >
            {loading ? 'جاري الحفظ...' : 'تأكيد وحفظ'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
