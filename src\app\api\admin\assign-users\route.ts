import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { requireSystemAdmin } from '@/lib/auth'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// POST - Assign users to areas/teams
export async function POST(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { user_ids, area_id, team_id, action } = await request.json()

    // Validate input
    if (!user_ids || !Array.isArray(user_ids) || user_ids.length === 0) {
      return NextResponse.json(
        { error: 'معرفات المستخدمين مطلوبة' },
        { status: 400 }
      )
    }

    if (!action || !['assign_area', 'assign_team', 'remove_assignments'].includes(action)) {
      return NextResponse.json(
        { error: 'نوع العملية غير صحيح' },
        { status: 400 }
      )
    }

    // Validate area if assigning to area
    if (action === 'assign_area' && area_id) {
      const { data: area, error: areaError } = await supabaseAdmin
        .from('areas')
        .select('id, name')
        .eq('id', area_id)
        .single()

      if (areaError || !area) {
        return NextResponse.json(
          { error: 'المنطقة المحددة غير موجودة' },
          { status: 400 }
        )
      }
    }

    // Validate team if assigning to team
    if (action === 'assign_team' && team_id) {
      const { data: team, error: teamError } = await supabaseAdmin
        .from('teams')
        .select('id, name, area_id')
        .eq('id', team_id)
        .single()

      if (teamError || !team) {
        return NextResponse.json(
          { error: 'الفريق المحدد غير موجود' },
          { status: 400 }
        )
      }

      // If assigning to team, also set the area
      if (!area_id) {
        return NextResponse.json(
          { error: 'يجب تحديد المنطقة عند تعيين فريق' },
          { status: 400 }
        )
      }
    }

    // Validate all user IDs exist
    const { data: users, error: usersError } = await supabaseAdmin
      .from('profiles')
      .select('id, full_name, email, role')
      .in('id', user_ids)

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب بيانات المستخدمين' },
        { status: 500 }
      )
    }

    if (!users || users.length !== user_ids.length) {
      return NextResponse.json(
        { error: 'بعض المستخدمين المحددين غير موجودين' },
        { status: 400 }
      )
    }

    // Check if any of the users are system admins
    const systemAdmins = users.filter(user => user.role === 'system_admin')
    if (systemAdmins.length > 0) {
      const adminNames = systemAdmins.map(admin => admin.full_name || admin.email).join(', ')
      return NextResponse.json(
        { error: `لا يمكن تعيين مديري النظام للمناطق أو الفرق: ${adminNames}` },
        { status: 400 }
      )
    }

    // Perform the assignment based on action
    let updateData: any = {}
    
    switch (action) {
      case 'assign_area':
        updateData = { 
          area_id: area_id || null,
          team_id: null // Clear team when assigning to area only
        }
        break
      
      case 'assign_team':
        updateData = { 
          area_id: area_id || null,
          team_id: team_id || null
        }
        break
      
      case 'remove_assignments':
        updateData = { 
          area_id: null,
          team_id: null
        }
        break
    }

    // Update all users
    const { data: updatedUsers, error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .in('id', user_ids)
      .select(`
        id,
        full_name,
        email,
        role,
        area_id,
        team_id,
        area:areas!profiles_area_id_fkey(id, name),
        team:teams!profiles_team_id_fkey(id, name)
      `)

    if (updateError) {
      console.error('Error updating users:', updateError)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث تعيينات المستخدمين' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      updated_users: updatedUsers,
      message: getSuccessMessage(action, updatedUsers?.length || 0)
    })

  } catch (error: any) {
    console.error('Assign users error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// GET - Get available users for assignment
export async function GET(request: NextRequest) {
  try {
    // Check if user is system admin
    await requireSystemAdmin()

    const { searchParams } = new URL(request.url)
    const filter = searchParams.get('filter') // 'unassigned', 'area_only', 'with_team', 'all'
    const area_id = searchParams.get('area_id')
    const team_id = searchParams.get('team_id')

    let query = supabaseAdmin
      .from('profiles')
      .select(`
        id,
        full_name,
        email,
        role,
        role_level,
        area_id,
        team_id,
        area:areas!profiles_area_id_fkey(id, name),
        team:teams!profiles_team_id_fkey(id, name)
      `)
      .order('full_name', { ascending: true })

    // Apply filters
    switch (filter) {
      case 'unassigned':
        query = query.is('area_id', null).is('team_id', null)
        break
      
      case 'area_only':
        query = query.not('area_id', 'is', null).is('team_id', null)
        break
      
      case 'with_team':
        query = query.not('team_id', 'is', null)
        break
      
      case 'by_area':
        if (area_id) {
          query = query.eq('area_id', area_id)
        }
        break
      
      case 'by_team':
        if (team_id) {
          query = query.eq('team_id', team_id)
        }
        break
    }

    const { data: users, error } = await query

    if (error) {
      console.error('Error fetching users for assignment:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب المستخدمين' },
        { status: 500 }
      )
    }

    return NextResponse.json({ users })

  } catch (error: any) {
    console.error('Get users for assignment error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

function getSuccessMessage(action: string, count: number): string {
  switch (action) {
    case 'assign_area':
      return `تم تعيين ${count} مستخدم للمنطقة بنجاح`
    case 'assign_team':
      return `تم تعيين ${count} مستخدم للفريق بنجاح`
    case 'remove_assignments':
      return `تم إلغاء تعيين ${count} مستخدم بنجاح`
    default:
      return `تم تحديث ${count} مستخدم بنجاح`
  }
}
